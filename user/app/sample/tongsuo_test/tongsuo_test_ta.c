/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo测试TA
 * 验证Tongsuo加密库在rctee TEE环境中的功能
 */

#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_user_ipc.h>
#include <uapi/err.h>
#include <libutee.h>
#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>
#include <unistd.h>

// Tongsuo加密库头文件 - 根据官方文档使用正确的头文件
#include <openssl/evp.h>
#include <openssl/err.h>
#include <openssl/ec.h>
#include <openssl/rand.h>
#include <openssl/aes.h>
#include <openssl/sha.h>
#include <openssl/rsa.h>
#include <openssl/bn.h>
#include <openssl/hmac.h>
#include <openssl/md5.h>

#define TONGSUO_PORT "8aaaf200-2450-11e4-abe2-0002a5d5c51b"
#define TLOG_TAG "tongsuo_test"
#define TONGSUO_MAX_BUFFER_LENGTH 1024

#define TEE_TEST_CMD_SM3_TEST                 1
#define TEE_TEST_CMD_SM4_TEST                 2
#define TEE_TEST_CMD_SM2_TEST                 3
#define TEE_TEST_CMD_RAND_TEST                4
#define TEE_TEST_CMD_SM2_SIGN_TEST            5
#define TEE_TEST_CMD_SM4_MODES_TEST           6
#define TEE_TEST_CMD_HMAC_SM3_TEST            7
#define TEE_TEST_CMD_SM2_KEYEXCHANGE_TEST     8
#define TEE_TEST_CMD_COMBINED_TEST            9
#define TEE_TEST_CMD_AES_256_CBC_TEST         10
#define TEE_TEST_CMD_SHA256_TEST              11
#define TEE_TEST_CMD_RSA_2048_TEST            12
#define TEE_TEST_CMD_HMAC_SHA256_TEST         13
#define TEE_TEST_CMD_CHACHA20_POLY1305_TEST   14
#define TEE_TEST_CMD_AES_LOW_LEVEL_TEST       15
#define TEE_TEST_CMD_SHA256_LOW_LEVEL_TEST    16
#define TEE_TEST_CMD_HMAC_LOW_LEVEL_TEST      17
#define TEE_TEST_CMD_BIGNUM_LOW_LEVEL_TEST    18
#define TEE_TEST_CMD_MD5_LOW_LEVEL_TEST       19

// 函数声明
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size);

// 辅助函数：打印十六进制数据
static void print_hex(const char *label, const unsigned char *data, size_t len) {
    TLOGI("%s (%zu bytes): ", label, len);
    for (size_t i = 0; i < len; i++) {
        if (i > 0 && i % 16 == 0) {
            TLOGI("\n");
        }
        TLOGI("%02x ", data[i]);
    }
    TLOGI("\n");
}



// SM3哈希算法测试 - 使用官方推荐的EVP接口
static int test_sm3_hash(void) {
    TLOGI("=== 测试SM3哈希算法 ===\n");

    const char *test_data = "abc";
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len = 0;

    // 使用EVP接口进行SM3哈希计算
    EVP_MD_CTX *mdctx = EVP_MD_CTX_new();
    if (mdctx == NULL) {
        TLOGE("❌ EVP_MD_CTX_new失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_DigestInit_ex(mdctx, EVP_sm3(), NULL)) {
        TLOGE("❌ EVP_DigestInit_ex失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    if (!EVP_DigestUpdate(mdctx, test_data, strlen(test_data))) {
        TLOGE("❌ EVP_DigestUpdate失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    if (!EVP_DigestFinal_ex(mdctx, hash, &hash_len)) {
        TLOGE("❌ EVP_DigestFinal_ex失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    EVP_MD_CTX_free(mdctx);

    TLOGI("输入数据: %s\n", test_data);
    print_hex("SM3哈希结果", hash, hash_len);

    // 验证已知测试向量 (GM/T 0004-2012 Example 1)
    unsigned char expected[] = {
        0x66, 0xc7, 0xf0, 0xf4, 0x62, 0xee, 0xed, 0xd9,
        0xd1, 0xf2, 0xd4, 0x6b, 0xdc, 0x10, 0xe4, 0xe2,
        0x41, 0x67, 0xc4, 0x87, 0x5c, 0xf2, 0xf7, 0xa2,
        0x29, 0x7d, 0xa0, 0x2b, 0x8f, 0x4b, 0xa8, 0xe0
    };

    if (hash_len == 32 && memcmp(hash, expected, 32) == 0) {
        TLOGI("✅ SM3哈希测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ SM3哈希结果不匹配\n");
        return ERR_GENERIC;
    }
}

// SM4对称加密测试 - 使用官方推荐的EVP接口和CBC模式
static int test_sm4_cipher(void) {
    TLOGI("=== 测试SM4对称加密 ===\n");

    // 测试密钥和IV
    unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
        0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10
    };

    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
    };

    const char *plaintext = "Winter Is Coming";
    size_t plaintext_len = strlen(plaintext);
    unsigned char ciphertext[64];
    unsigned char decrypted[64];
    int ciphertext_len = 0, decrypted_len = 0;
    int tmplen = 0;

    // 加密
    EVP_CIPHER_CTX *encrypt_ctx = EVP_CIPHER_CTX_new();
    if (encrypt_ctx == NULL) {
        TLOGE("❌ EVP_CIPHER_CTX_new失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_EncryptInit_ex(encrypt_ctx, EVP_sm4_cbc(), NULL, key, iv)) {
        TLOGE("❌ EVP_EncryptInit_ex失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_EncryptUpdate(encrypt_ctx, ciphertext, &ciphertext_len,
                          (unsigned char*)plaintext, plaintext_len)) {
        TLOGE("❌ EVP_EncryptUpdate失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_EncryptFinal_ex(encrypt_ctx, ciphertext + ciphertext_len, &tmplen)) {
        TLOGE("❌ EVP_EncryptFinal_ex失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }
    ciphertext_len += tmplen;
    EVP_CIPHER_CTX_free(encrypt_ctx);

    TLOGI("明文: %s\n", plaintext);
    print_hex("SM4加密结果", ciphertext, ciphertext_len);

    // 解密
    EVP_CIPHER_CTX *decrypt_ctx = EVP_CIPHER_CTX_new();
    if (decrypt_ctx == NULL) {
        TLOGE("❌ EVP_CIPHER_CTX_new失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_DecryptInit_ex(decrypt_ctx, EVP_sm4_cbc(), NULL, key, iv)) {
        TLOGE("❌ EVP_DecryptInit_ex失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptUpdate(decrypt_ctx, decrypted, &decrypted_len,
                          ciphertext, ciphertext_len)) {
        TLOGE("❌ EVP_DecryptUpdate失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptFinal_ex(decrypt_ctx, decrypted + decrypted_len, &tmplen)) {
        TLOGE("❌ EVP_DecryptFinal_ex失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }
    decrypted_len += tmplen;
    EVP_CIPHER_CTX_free(decrypt_ctx);

    // 添加字符串结束符
    decrypted[decrypted_len] = '\0';
    TLOGI("解密结果: %s\n", (char*)decrypted);

    // 验证解密结果
    if (decrypted_len == plaintext_len &&
        memcmp(plaintext, decrypted, plaintext_len) == 0) {
        TLOGI("✅ SM4加密/解密测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ SM4解密结果不匹配\n");
        return ERR_GENERIC;
    }
}

// SM2椭圆曲线测试 - 使用官方推荐的EVP接口
static int test_sm2_basic(void) {
    TLOGI("=== 测试SM2椭圆曲线基础功能 ===\n");

    // 生成SM2密钥对 - 使用EVP接口
    EVP_PKEY_CTX *pkey_ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_EC, NULL);
    if (pkey_ctx == NULL) {
        TLOGE("❌ EVP_PKEY_CTX_new_id失败\n");
        return ERR_GENERIC;
    }

    if (EVP_PKEY_keygen_init(pkey_ctx) <= 0) {
        TLOGE("❌ EVP_PKEY_keygen_init失败\n");
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_CTX_set_ec_paramgen_curve_nid(pkey_ctx, OBJ_sn2nid("SM2")) <= 0) {
        TLOGE("❌ EVP_PKEY_CTX_set_ec_paramgen_curve_nid失败\n");
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    EVP_PKEY *sm2_key = NULL;
    if (EVP_PKEY_keygen(pkey_ctx, &sm2_key) <= 0) {
        TLOGE("❌ EVP_PKEY_keygen失败\n");
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_set_alias_type(sm2_key, EVP_PKEY_SM2) <= 0) {
        TLOGE("❌ EVP_PKEY_set_alias_type失败\n");
        EVP_PKEY_free(sm2_key);
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    EVP_PKEY_CTX_free(pkey_ctx);

    TLOGI("✅ SM2密钥对生成成功\n");

    // 测试SM2加密/解密
    const char *plaintext = "Hello SM2";
    size_t plaintext_len = strlen(plaintext);
    unsigned char *ciphertext = NULL;
    size_t ciphertext_len = 0;

    // 加密
    EVP_PKEY_CTX *encrypt_ctx = EVP_PKEY_CTX_new(sm2_key, NULL);
    if (encrypt_ctx == NULL) {
        TLOGE("❌ 创建加密上下文失败\n");
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_encrypt_init(encrypt_ctx) <= 0) {
        TLOGE("❌ EVP_PKEY_encrypt_init失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    // 获取密文长度
    if (EVP_PKEY_encrypt(encrypt_ctx, NULL, &ciphertext_len,
                        (unsigned char*)plaintext, plaintext_len) <= 0) {
        TLOGE("❌ 获取密文长度失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    ciphertext = malloc(ciphertext_len);
    if (ciphertext == NULL) {
        TLOGE("❌ 分配密文内存失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_NO_MEMORY;
    }

    // 执行加密
    if (EVP_PKEY_encrypt(encrypt_ctx, ciphertext, &ciphertext_len,
                        (unsigned char*)plaintext, plaintext_len) <= 0) {
        TLOGE("❌ SM2加密失败\n");
        free(ciphertext);
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    EVP_PKEY_CTX_free(encrypt_ctx);

    TLOGI("明文: %s\n", plaintext);
    print_hex("SM2加密结果", ciphertext, ciphertext_len);

    // 解密
    EVP_PKEY_CTX *decrypt_ctx = EVP_PKEY_CTX_new(sm2_key, NULL);
    if (decrypt_ctx == NULL) {
        TLOGE("❌ 创建解密上下文失败\n");
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_decrypt_init(decrypt_ctx) <= 0) {
        TLOGE("❌ EVP_PKEY_decrypt_init失败\n");
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    unsigned char *decrypted = NULL;
    size_t decrypted_len = 0;

    // 获取明文长度
    if (EVP_PKEY_decrypt(decrypt_ctx, NULL, &decrypted_len,
                        ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ 获取明文长度失败\n");
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    decrypted = malloc(decrypted_len + 1);
    if (decrypted == NULL) {
        TLOGE("❌ 分配明文内存失败\n");
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_NO_MEMORY;
    }

    // 执行解密
    if (EVP_PKEY_decrypt(decrypt_ctx, decrypted, &decrypted_len,
                        ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ SM2解密失败\n");
        free(decrypted);
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    decrypted[decrypted_len] = '\0';
    TLOGI("解密结果: %s\n", (char*)decrypted);

    // 验证结果
    int result = TEE_SUCCESS;
    if (decrypted_len != plaintext_len ||
        memcmp(plaintext, decrypted, plaintext_len) != 0) {
        TLOGE("❌ SM2解密结果不匹配\n");
        result = ERR_GENERIC;
    } else {
        TLOGI("✅ SM2加密/解密测试通过\n");
    }

    // 清理资源
    free(decrypted);
    free(ciphertext);
    EVP_PKEY_CTX_free(decrypt_ctx);
    EVP_PKEY_free(sm2_key);

    if (result == TEE_SUCCESS) {
        TLOGI("✅ SM2基础功能测试通过\n");
    }

    return result;
}

/*
 * AES-256-CBC加密算法测试
 */
static int test_aes_256_cbc(void) {
    TLOGI("=== 测试AES-256-CBC加密算法 ===\n");

    const char *plaintext = "Hello, Tongsuo AES-256-CBC! This is a test message for encryption.";
    size_t plaintext_len = strlen(plaintext);

    // 256位密钥 (32字节)
    unsigned char key[32] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
        0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
        0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20
    };

    // 128位初始化向量 (16字节)
    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };

    unsigned char ciphertext[128];
    unsigned char decrypted[128];
    int len, ciphertext_len = 0, decrypted_len = 0;

    // 加密过程
    EVP_CIPHER_CTX *encrypt_ctx = EVP_CIPHER_CTX_new();
    if (!encrypt_ctx) {
        TLOGE("❌ 创建AES加密上下文失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_EncryptInit_ex(encrypt_ctx, EVP_aes_256_cbc(), NULL, key, iv)) {
        TLOGE("❌ 初始化AES加密失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_EncryptUpdate(encrypt_ctx, ciphertext, &len, (unsigned char*)plaintext, plaintext_len)) {
        TLOGE("❌ AES加密更新失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }
    ciphertext_len = len;

    if (!EVP_EncryptFinal_ex(encrypt_ctx, ciphertext + len, &len)) {
        TLOGE("❌ AES加密完成失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }
    ciphertext_len += len;
    EVP_CIPHER_CTX_free(encrypt_ctx);

    TLOGI("AES-256-CBC加密成功，密文长度: %d字节\n", ciphertext_len);
    print_hex("AES密文", ciphertext, ciphertext_len > 32 ? 32 : ciphertext_len);

    // 解密过程
    EVP_CIPHER_CTX *decrypt_ctx = EVP_CIPHER_CTX_new();
    if (!decrypt_ctx) {
        TLOGE("❌ 创建AES解密上下文失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_DecryptInit_ex(decrypt_ctx, EVP_aes_256_cbc(), NULL, key, iv)) {
        TLOGE("❌ 初始化AES解密失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptUpdate(decrypt_ctx, decrypted, &len, ciphertext, ciphertext_len)) {
        TLOGE("❌ AES解密更新失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }
    decrypted_len = len;

    if (!EVP_DecryptFinal_ex(decrypt_ctx, decrypted + len, &len)) {
        TLOGE("❌ AES解密完成失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }
    decrypted_len += len;
    EVP_CIPHER_CTX_free(decrypt_ctx);

    decrypted[decrypted_len] = '\0';
    TLOGI("AES-256-CBC解密成功，明文: %s\n", (char*)decrypted);

    // 验证解密结果
    if (decrypted_len == plaintext_len && memcmp(plaintext, decrypted, plaintext_len) == 0) {
        TLOGI("✅ AES-256-CBC加解密测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ AES-256-CBC加解密验证失败\n");
        return ERR_GENERIC;
    }
}

static int test_random_generation(void) {
    TLOGI("=== 测试随机数生成 ===\n");

    unsigned char random_bytes[32];

    if (RAND_bytes(random_bytes, sizeof(random_bytes)) != 1) {
        TLOGE("❌ RAND_bytes失败\n");
        return ERR_GENERIC;
    }

    print_hex("随机数", random_bytes, sizeof(random_bytes));

    // 简单检查：确保不是全零
    bool all_zero = true;
    for (size_t i = 0; i < sizeof(random_bytes); i++) {
        if (random_bytes[i] != 0) {
            all_zero = false;
            break;
        }
    }

    if (all_zero) {
        TLOGE("❌ 随机数生成失败（全零）\n");
        return ERR_GENERIC;
    }

    TLOGI("✅ 随机数生成测试通过\n");
    return TEE_SUCCESS;
}

/*
 * SHA-256哈希算法测试
 */
static int test_sha256_hash(void) {
    TLOGI("=== 测试SHA-256哈希算法 ===\n");

    const char *test_data = "Hello, Tongsuo SHA-256! This is a test message.";
    unsigned char hash[32];  // SHA-256输出256位(32字节)
    unsigned int hash_len;

    EVP_MD_CTX *mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        TLOGE("❌ 创建SHA-256上下文失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_DigestInit_ex(mdctx, EVP_sha256(), NULL)) {
        TLOGE("❌ EVP_DigestInit_ex失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    if (!EVP_DigestUpdate(mdctx, test_data, strlen(test_data))) {
        TLOGE("❌ EVP_DigestUpdate失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    if (!EVP_DigestFinal_ex(mdctx, hash, &hash_len)) {
        TLOGE("❌ EVP_DigestFinal_ex失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    EVP_MD_CTX_free(mdctx);

    TLOGI("输入数据: %s\n", test_data);
    print_hex("SHA-256哈希结果", hash, hash_len);

    if (hash_len == 32) {
        TLOGI("✅ SHA-256哈希测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ SHA-256哈希长度不正确\n");
        return ERR_GENERIC;
    }
}

/*
 * RSA-2048密钥生成和加解密测试
 */
static int test_rsa_2048(void) {
    TLOGI("=== 测试RSA-2048加密算法 ===\n");

    // 生成RSA密钥对 - 使用Tongsuo的Q_keygen方法
    EVP_PKEY *rsa_key = EVP_PKEY_Q_keygen(NULL, NULL, "RSA", (size_t)2048);
    if (!rsa_key) {
        TLOGE("❌ RSA-2048密钥生成失败\n");
        return ERR_GENERIC;
    }

    TLOGI("✅ RSA-2048密钥对生成成功\n");

    // 测试RSA加密/解密
    const char *plaintext = "Hello RSA-2048!";
    size_t plaintext_len = strlen(plaintext);
    unsigned char *ciphertext = NULL;
    size_t ciphertext_len = 0;

    // 加密
    EVP_PKEY_CTX *encrypt_ctx = EVP_PKEY_CTX_new(rsa_key, NULL);
    if (!encrypt_ctx) {
        TLOGE("❌ 创建RSA加密上下文失败\n");
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_encrypt_init(encrypt_ctx) <= 0) {
        TLOGE("❌ RSA加密初始化失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    // 获取密文长度
    if (EVP_PKEY_encrypt(encrypt_ctx, NULL, &ciphertext_len, (unsigned char*)plaintext, plaintext_len) <= 0) {
        TLOGE("❌ 获取RSA密文长度失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    ciphertext = malloc(ciphertext_len);
    if (!ciphertext) {
        TLOGE("❌ 分配RSA密文内存失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_encrypt(encrypt_ctx, ciphertext, &ciphertext_len, (unsigned char*)plaintext, plaintext_len) <= 0) {
        TLOGE("❌ RSA加密失败\n");
        free(ciphertext);
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    EVP_PKEY_CTX_free(encrypt_ctx);
    TLOGI("RSA-2048加密成功，密文长度: %zu字节\n", ciphertext_len);

    // 解密
    EVP_PKEY_CTX *decrypt_ctx = EVP_PKEY_CTX_new(rsa_key, NULL);
    if (!decrypt_ctx) {
        TLOGE("❌ 创建RSA解密上下文失败\n");
        free(ciphertext);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_decrypt_init(decrypt_ctx) <= 0) {
        TLOGE("❌ RSA解密初始化失败\n");
        free(ciphertext);
        EVP_PKEY_CTX_free(decrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    size_t decrypted_len = 0;
    if (EVP_PKEY_decrypt(decrypt_ctx, NULL, &decrypted_len, ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ 获取RSA解密长度失败\n");
        free(ciphertext);
        EVP_PKEY_CTX_free(decrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    unsigned char *decrypted = malloc(decrypted_len);
    if (!decrypted) {
        TLOGE("❌ 分配RSA解密内存失败\n");
        free(ciphertext);
        EVP_PKEY_CTX_free(decrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_decrypt(decrypt_ctx, decrypted, &decrypted_len, ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ RSA解密失败\n");
        free(decrypted);
        free(ciphertext);
        EVP_PKEY_CTX_free(decrypt_ctx);
        EVP_PKEY_free(rsa_key);
        return ERR_GENERIC;
    }

    decrypted[decrypted_len] = '\0';
    TLOGI("RSA-2048解密成功，明文: %s\n", (char*)decrypted);

    // 验证解密结果
    int result = TEE_SUCCESS;
    if (decrypted_len != plaintext_len || memcmp(plaintext, decrypted, plaintext_len) != 0) {
        TLOGE("❌ RSA-2048加解密验证失败\n");
        result = ERR_GENERIC;
    } else {
        TLOGI("✅ RSA-2048加解密测试通过\n");
    }

    // 清理资源
    free(decrypted);
    free(ciphertext);
    EVP_PKEY_CTX_free(decrypt_ctx);
    EVP_PKEY_free(rsa_key);

    return result;
}

/*
 * HMAC-SHA256消息认证码测试
 */
static int test_hmac_sha256(void) {
    TLOGI("=== 测试HMAC-SHA256消息认证码 ===\n");

    const char *message = "Hello, Tongsuo HMAC-SHA256! This is a test message.";
    const char *key = "secret_key_for_hmac_test";
    unsigned char hmac[32];  // HMAC-SHA256输出256位(32字节)
    unsigned int hmac_len;

    EVP_PKEY *pkey = EVP_PKEY_new_mac_key(EVP_PKEY_HMAC, NULL, (unsigned char*)key, strlen(key));
    if (!pkey) {
        TLOGE("❌ 创建HMAC密钥失败\n");
        return ERR_GENERIC;
    }

    EVP_MD_CTX *mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        TLOGE("❌ 创建HMAC上下文失败\n");
        EVP_PKEY_free(pkey);
        return ERR_GENERIC;
    }

    if (EVP_DigestSignInit(mdctx, NULL, EVP_sha256(), NULL, pkey) != 1) {
        TLOGE("❌ HMAC初始化失败\n");
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        return ERR_GENERIC;
    }

    if (EVP_DigestSignUpdate(mdctx, message, strlen(message)) != 1) {
        TLOGE("❌ HMAC更新失败\n");
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        return ERR_GENERIC;
    }

    size_t hmac_len_size_t = sizeof(hmac);
    if (EVP_DigestSignFinal(mdctx, hmac, &hmac_len_size_t) != 1) {
        TLOGE("❌ HMAC完成失败\n");
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        return ERR_GENERIC;
    }
    hmac_len = (unsigned int)hmac_len_size_t;

    EVP_MD_CTX_free(mdctx);
    EVP_PKEY_free(pkey);

    TLOGI("输入消息: %s\n", message);
    TLOGI("HMAC密钥: %s\n", key);
    print_hex("HMAC-SHA256结果", hmac, hmac_len);

    if (hmac_len == 32) {
        TLOGI("✅ HMAC-SHA256测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ HMAC-SHA256长度不正确\n");
        return ERR_GENERIC;
    }
}

/*
 * ChaCha20-Poly1305 AEAD加密测试
 */
static int test_chacha20_poly1305(void) {
    TLOGI("=== 测试ChaCha20-Poly1305 AEAD加密 ===\n");

    const char *plaintext = "Hello, Tongsuo ChaCha20-Poly1305! This is an AEAD test.";
    size_t plaintext_len = strlen(plaintext);

    // 256位密钥 (32字节)
    unsigned char key[32] = {
        0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
        0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
        0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
        0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
    };

    // 96位nonce (12字节)
    unsigned char nonce[12] = {
        0x07, 0x00, 0x00, 0x00, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47
    };

    unsigned char ciphertext[128];
    unsigned char tag[16];  // 认证标签
    unsigned char decrypted[128];
    int len, ciphertext_len = 0, decrypted_len = 0;

    // 加密过程
    EVP_CIPHER_CTX *encrypt_ctx = EVP_CIPHER_CTX_new();
    if (!encrypt_ctx) {
        TLOGE("❌ 创建ChaCha20-Poly1305加密上下文失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_EncryptInit_ex(encrypt_ctx, EVP_chacha20_poly1305(), NULL, NULL, NULL)) {
        TLOGE("❌ 初始化ChaCha20-Poly1305失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_CIPHER_CTX_ctrl(encrypt_ctx, EVP_CTRL_AEAD_SET_IVLEN, 12, NULL)) {
        TLOGE("❌ 设置ChaCha20-Poly1305 nonce长度失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_EncryptInit_ex(encrypt_ctx, NULL, NULL, key, nonce)) {
        TLOGE("❌ 设置ChaCha20-Poly1305密钥和nonce失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_EncryptUpdate(encrypt_ctx, ciphertext, &len, (unsigned char*)plaintext, plaintext_len)) {
        TLOGE("❌ ChaCha20-Poly1305加密更新失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }
    ciphertext_len = len;

    if (!EVP_EncryptFinal_ex(encrypt_ctx, ciphertext + len, &len)) {
        TLOGE("❌ ChaCha20-Poly1305加密完成失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }
    ciphertext_len += len;

    if (!EVP_CIPHER_CTX_ctrl(encrypt_ctx, EVP_CTRL_AEAD_GET_TAG, 16, tag)) {
        TLOGE("❌ 获取ChaCha20-Poly1305认证标签失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    EVP_CIPHER_CTX_free(encrypt_ctx);

    TLOGI("ChaCha20-Poly1305加密成功，密文长度: %d字节\n", ciphertext_len);
    print_hex("ChaCha20密文", ciphertext, ciphertext_len > 32 ? 32 : ciphertext_len);
    print_hex("Poly1305认证标签", tag, 16);

    // 解密过程
    EVP_CIPHER_CTX *decrypt_ctx = EVP_CIPHER_CTX_new();
    if (!decrypt_ctx) {
        TLOGE("❌ 创建ChaCha20-Poly1305解密上下文失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_DecryptInit_ex(decrypt_ctx, EVP_chacha20_poly1305(), NULL, NULL, NULL)) {
        TLOGE("❌ 初始化ChaCha20-Poly1305解密失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_CIPHER_CTX_ctrl(decrypt_ctx, EVP_CTRL_AEAD_SET_IVLEN, 12, NULL)) {
        TLOGE("❌ 设置ChaCha20-Poly1305解密nonce长度失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptInit_ex(decrypt_ctx, NULL, NULL, key, nonce)) {
        TLOGE("❌ 设置ChaCha20-Poly1305解密密钥和nonce失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptUpdate(decrypt_ctx, decrypted, &len, ciphertext, ciphertext_len)) {
        TLOGE("❌ ChaCha20-Poly1305解密更新失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }
    decrypted_len = len;

    if (!EVP_CIPHER_CTX_ctrl(decrypt_ctx, EVP_CTRL_AEAD_SET_TAG, 16, tag)) {
        TLOGE("❌ 设置ChaCha20-Poly1305认证标签失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptFinal_ex(decrypt_ctx, decrypted + len, &len)) {
        TLOGE("❌ ChaCha20-Poly1305解密完成失败（认证失败）\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }
    decrypted_len += len;

    EVP_CIPHER_CTX_free(decrypt_ctx);

    decrypted[decrypted_len] = '\0';
    TLOGI("ChaCha20-Poly1305解密成功，明文: %s\n", (char*)decrypted);

    // 验证解密结果
    if (decrypted_len == plaintext_len && memcmp(plaintext, decrypted, plaintext_len) == 0) {
        TLOGI("✅ ChaCha20-Poly1305 AEAD测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ ChaCha20-Poly1305 AEAD验证失败\n");
        return ERR_GENERIC;
    }
}

/*
 * AES低级API测试 - 直接使用AES_encrypt/AES_decrypt
 */
static int test_aes_low_level(void) {
    TLOGI("=== 测试AES低级API ===\n");

    // 128位密钥 (16字节)
    unsigned char key[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };

    // 128位明文块 (16字节)
    unsigned char plaintext[16] = {
        0x32, 0x43, 0xf6, 0xa8, 0x88, 0x5a, 0x30, 0x8d,
        0x31, 0x31, 0x98, 0xa2, 0xe0, 0x37, 0x07, 0x34
    };

    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    AES_KEY encrypt_key, decrypt_key;

    // 设置加密密钥
    if (AES_set_encrypt_key(key, 128, &encrypt_key) != 0) {
        TLOGE("❌ AES设置加密密钥失败\n");
        return ERR_GENERIC;
    }

    // 设置解密密钥
    if (AES_set_decrypt_key(key, 128, &decrypt_key) != 0) {
        TLOGE("❌ AES设置解密密钥失败\n");
        return ERR_GENERIC;
    }

    // 加密单个块
    AES_encrypt(plaintext, ciphertext, &encrypt_key);
    TLOGI("AES低级API加密成功\n");
    print_hex("明文", plaintext, 16);
    print_hex("密文", ciphertext, 16);

    // 解密单个块
    AES_decrypt(ciphertext, decrypted, &decrypt_key);
    TLOGI("AES低级API解密成功\n");
    print_hex("解密结果", decrypted, 16);

    // 验证解密结果
    if (memcmp(plaintext, decrypted, 16) == 0) {
        TLOGI("✅ AES低级API测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ AES低级API验证失败\n");
        return ERR_GENERIC;
    }
}

/*
 * SHA-256低级API测试 - 直接使用SHA256_Init/Update/Final
 */
static int test_sha256_low_level(void) {
    TLOGI("=== 测试SHA-256低级API ===\n");

    const char *test_data = "Hello, Tongsuo SHA-256 Low Level API!";
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX ctx;

    // 初始化SHA-256上下文
    if (!SHA256_Init(&ctx)) {
        TLOGE("❌ SHA256_Init失败\n");
        return ERR_GENERIC;
    }

    // 更新数据
    if (!SHA256_Update(&ctx, test_data, strlen(test_data))) {
        TLOGE("❌ SHA256_Update失败\n");
        return ERR_GENERIC;
    }

    // 完成哈希计算
    if (!SHA256_Final(hash, &ctx)) {
        TLOGE("❌ SHA256_Final失败\n");
        return ERR_GENERIC;
    }

    TLOGI("输入数据: %s\n", test_data);
    print_hex("SHA-256哈希结果", hash, SHA256_DIGEST_LENGTH);

    TLOGI("✅ SHA-256低级API测试通过\n");
    return TEE_SUCCESS;
}

/*
 * HMAC低级API测试 - 直接使用HMAC_Init/Update/Final
 */
static int test_hmac_low_level(void) {
    TLOGI("=== 测试HMAC低级API ===\n");

    const char *message = "Hello, Tongsuo HMAC Low Level API!";
    const char *key = "secret_key_for_hmac_low_level_test";
    unsigned char hmac_result[EVP_MAX_MD_SIZE];
    unsigned int hmac_len;
    HMAC_CTX *ctx;

    // 创建HMAC上下文
    ctx = HMAC_CTX_new();
    if (!ctx) {
        TLOGE("❌ HMAC_CTX_new失败\n");
        return ERR_GENERIC;
    }

    // 初始化HMAC
    if (!HMAC_Init_ex(ctx, key, strlen(key), EVP_sha256(), NULL)) {
        TLOGE("❌ HMAC_Init_ex失败\n");
        HMAC_CTX_free(ctx);
        return ERR_GENERIC;
    }

    // 更新数据
    if (!HMAC_Update(ctx, (unsigned char*)message, strlen(message))) {
        TLOGE("❌ HMAC_Update失败\n");
        HMAC_CTX_free(ctx);
        return ERR_GENERIC;
    }

    // 完成HMAC计算
    if (!HMAC_Final(ctx, hmac_result, &hmac_len)) {
        TLOGE("❌ HMAC_Final失败\n");
        HMAC_CTX_free(ctx);
        return ERR_GENERIC;
    }

    HMAC_CTX_free(ctx);

    TLOGI("输入消息: %s\n", message);
    TLOGI("HMAC密钥: %s\n", key);
    print_hex("HMAC-SHA256结果", hmac_result, hmac_len);

    TLOGI("✅ HMAC低级API测试通过\n");
    return TEE_SUCCESS;
}

/*
 * 大数运算低级API测试 - 直接使用BIGNUM API
 */
static int test_bignum_low_level(void) {
    TLOGI("=== 测试大数运算低级API ===\n");

    BIGNUM *a = NULL, *b = NULL, *result = NULL;
    BN_CTX *ctx = NULL;
    char *a_str = NULL, *b_str = NULL, *result_str = NULL;
    int ret = ERR_GENERIC;

    // 创建大数上下文
    ctx = BN_CTX_new();
    if (!ctx) {
        TLOGE("❌ BN_CTX_new失败\n");
        goto cleanup;
    }

    // 创建大数
    a = BN_new();
    b = BN_new();
    result = BN_new();
    if (!a || !b || !result) {
        TLOGE("❌ BN_new失败\n");
        goto cleanup;
    }

    // 设置大数值
    if (!BN_set_word(a, 12345678) || !BN_set_word(b, 87654321)) {
        TLOGE("❌ BN_set_word失败\n");
        goto cleanup;
    }

    // 大数乘法
    if (!BN_mul(result, a, b, ctx)) {
        TLOGE("❌ BN_mul失败\n");
        goto cleanup;
    }

    // 转换为字符串显示
    a_str = BN_bn2dec(a);
    b_str = BN_bn2dec(b);
    result_str = BN_bn2dec(result);

    if (a_str && b_str && result_str) {
        TLOGI("大数运算: %s × %s = %s\n", a_str, b_str, result_str);
        TLOGI("✅ 大数运算低级API测试通过\n");
        ret = TEE_SUCCESS;
    } else {
        TLOGE("❌ BN_bn2dec失败\n");
    }

cleanup:
    if (a_str) OPENSSL_free(a_str);
    if (b_str) OPENSSL_free(b_str);
    if (result_str) OPENSSL_free(result_str);
    if (a) BN_free(a);
    if (b) BN_free(b);
    if (result) BN_free(result);
    if (ctx) BN_CTX_free(ctx);

    return ret;
}

/*
 * MD5低级API测试 - 直接使用MD5_Init/Update/Final
 */
static int test_md5_low_level(void) {
    TLOGI("=== 测试MD5低级API ===\n");

    const char *test_data = "Hello, Tongsuo MD5 Low Level API!";
    unsigned char hash[MD5_DIGEST_LENGTH];
    MD5_CTX ctx;

    // 初始化MD5上下文
    if (!MD5_Init(&ctx)) {
        TLOGE("❌ MD5_Init失败\n");
        return ERR_GENERIC;
    }

    // 更新数据
    if (!MD5_Update(&ctx, test_data, strlen(test_data))) {
        TLOGE("❌ MD5_Update失败\n");
        return ERR_GENERIC;
    }

    // 完成哈希计算
    if (!MD5_Final(hash, &ctx)) {
        TLOGE("❌ MD5_Final失败\n");
        return ERR_GENERIC;
    }

    TLOGI("输入数据: %s\n", test_data);
    print_hex("MD5哈希结果", hash, MD5_DIGEST_LENGTH);

    TLOGI("✅ MD5低级API测试通过\n");
    return TEE_SUCCESS;
}

// SM2测试函数暂时移除，等基本功能稳定后再添加

// rctee TA命令处理函数
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size) {

    TLOGI("Tongsuo测试TA命令调用: %u\n", cmd);

    switch (cmd) {
    case TEE_TEST_CMD_SM3_TEST:
        TLOGI("处理SM3哈希测试命令\n");
        return test_sm3_hash();

    case TEE_TEST_CMD_SM4_TEST:
        TLOGI("处理SM4加密测试命令\n");
        return test_sm4_cipher();

    case TEE_TEST_CMD_SM2_TEST:
        TLOGI("处理SM2椭圆曲线测试命令\n");
        return test_sm2_basic();

    case TEE_TEST_CMD_RAND_TEST:
        TLOGI("处理随机数生成测试命令\n");
        return test_random_generation();

    case TEE_TEST_CMD_AES_256_CBC_TEST:
        TLOGI("处理AES-256-CBC加密测试命令\n");
        return test_aes_256_cbc();

    case TEE_TEST_CMD_SHA256_TEST:
        TLOGI("处理SHA-256哈希测试命令\n");
        return test_sha256_hash();

    case TEE_TEST_CMD_RSA_2048_TEST:
        TLOGI("处理RSA-2048加密测试命令\n");
        return test_rsa_2048();

    case TEE_TEST_CMD_HMAC_SHA256_TEST:
        TLOGI("处理HMAC-SHA256测试命令\n");
        return test_hmac_sha256();

    case TEE_TEST_CMD_CHACHA20_POLY1305_TEST:
        TLOGI("处理ChaCha20-Poly1305 AEAD测试命令\n");
        return test_chacha20_poly1305();

    case TEE_TEST_CMD_AES_LOW_LEVEL_TEST:
        TLOGI("处理AES低级API测试命令\n");
        return test_aes_low_level();

    case TEE_TEST_CMD_SHA256_LOW_LEVEL_TEST:
        TLOGI("处理SHA-256低级API测试命令\n");
        return test_sha256_low_level();

    case TEE_TEST_CMD_HMAC_LOW_LEVEL_TEST:
        TLOGI("处理HMAC低级API测试命令\n");
        return test_hmac_low_level();

    case TEE_TEST_CMD_BIGNUM_LOW_LEVEL_TEST:
        TLOGI("处理大数运算低级API测试命令\n");
        return test_bignum_low_level();

    case TEE_TEST_CMD_MD5_LOW_LEVEL_TEST:
        TLOGI("处理MD5低级API测试命令\n");
        return test_md5_low_level();

    default:
        TLOGE("未知命令: %u\n", cmd);
        return ERR_INVALID_ARGS;
    }
}

// rctee TA入口点函数
int RCTEE_OnConnect(void) {
    TLOGI("Tongsuo测试TA客户端连接\n");
    return TEE_SUCCESS;
}

void RCTEE_OnDisConnect(void* cookie) {
    TLOGI("Tongsuo测试TA客户端断开连接\n");
}

int RCTEE_OnInit(void) {
    TLOGI("Tongsuo测试TA初始化\n");

    // 运行所有测试
    TLOGI("==========================================\n");
    TLOGI("    Tongsuo rctee TEE 集成测试\n");
    TLOGI("==========================================\n");

    int result = TEE_SUCCESS;

    // 测试随机数生成
    if (test_random_generation() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SM3哈希
    if (test_sm3_hash() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SM4加密
    if (test_sm4_cipher() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SM2椭圆曲线
    if (test_sm2_basic() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试AES-256-CBC加密
    if (test_aes_256_cbc() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SHA-256哈希
    if (test_sha256_hash() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试RSA-2048加密
    if (test_rsa_2048() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试HMAC-SHA256
    if (test_hmac_sha256() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试ChaCha20-Poly1305 AEAD
    if (test_chacha20_poly1305() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    TLOGI("==========================================\n");
    TLOGI("    开始低级API测试\n");
    TLOGI("==========================================\n");

    // 测试AES低级API
    if (test_aes_low_level() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SHA-256低级API
    if (test_sha256_low_level() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试HMAC低级API
    if (test_hmac_low_level() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试大数运算低级API
    if (test_bignum_low_level() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试MD5低级API
    if (test_md5_low_level() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    if (result == TEE_SUCCESS) {
        TLOGI("==========================================\n");
        TLOGI("    ✅ 所有Tongsuo测试通过！\n");
        TLOGI("==========================================\n");
    } else {
        TLOGI("==========================================\n");
        TLOGI("    ❌ 部分Tongsuo测试失败！\n");
        TLOGI("==========================================\n");
    }

    return result;
}

// 注意: main函数由libutee库提供，这里不需要定义
